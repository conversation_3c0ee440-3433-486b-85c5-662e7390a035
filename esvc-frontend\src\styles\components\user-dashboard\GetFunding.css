/* Get Funding Content */
.get-funding-content {
  position: relative;
  z-index: 2;
}

/* Additional gradient blur under body */
.get-funding-content::before {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  left: 0px;
  top: 400px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

.get-funding-content::after {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  right: 0px;
  top: 600px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

/* User Header */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 60px 40px 40px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Desktop: Position controls outside greeting */
@media (min-width: 769px) {
  .user-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .user-greeting {
    flex: 1;
    position: relative;
  }

  .header-controls {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }
}

/* User Header Background */
.user-header::before {
  content: '';
  position: absolute;
  top: -120px;
  left: -50vw;
  right: -50vw;
  height: 400px; /* Extended to cover button and toggle */
  background: #260D08;
  z-index: -1;
}

/* User Greeting Styles */
.greeting-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.greeting-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  color: #CCCCCC;
  margin: 0 0 32px 0;
  font-weight: 400;
}

/* Header Controls */
.stake-esvc-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-esvc-btn:hover {
  background: #A63622;
  transform: translateY(-2px);
}

.stake-esvc-btn .btn-icon {
  width: 20px;
  height: 20px;
}

/* Balance Toggle */
.balance-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #404040;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: #FFFFFF;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #BF4129;
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

/* Dashboard Layout */
.get-funding-container .dashboard-layout {
  display: flex;
  gap: 32px;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  margin-top: 60px;
}

.funding-main-content {
  flex: 1;
  max-width: calc(100% - 280px - 32px);
}

/* Eligibility Tabs */
.eligibility-tabs {
  display: flex;
  gap: 0;
  margin-bottom: 24px;
  background: #262626;
  border-radius: 12px;
  padding: 4px;
  width: fit-content;
}

.eligibility-tab {
  padding: 12px 24px;
  background: none;
  border: none;
  border-radius: 8px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.eligibility-tab:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.eligibility-tab.active {
  background: #404040;
  color: #FFFFFF;
}

/* Funding Card */
.funding-card {
  background: #1A1A1A;
  border: 1px solid #333333;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
}

.funding-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 24px 0;
}

/* Not Eligible Content */
.not-eligible-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.warning-message {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  background: rgba(191, 65, 41, 0.1);
  border: 1px solid rgba(191, 65, 41, 0.3);
  border-radius: 12px;
  padding: 20px;
}

.warning-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  margin-top: 2px;
}

.warning-text {
  flex: 1;
}

.warning-text p {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #FFFFFF;
  margin: 0 0 12px 0;
  font-weight: 500;
}

.warning-text ul {
  margin: 12px 0 0 0;
  padding-left: 20px;
}

.warning-text li {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  margin-bottom: 8px;
  font-weight: 400;
}

.warning-text strong {
  color: #FFFFFF;
  font-weight: 600;
}

.stake-qualify-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: fit-content;
}

.stake-qualify-btn:hover {
  background: #A63622;
  transform: translateY(-2px);
}

.stake-qualify-btn .btn-icon {
  width: 20px;
  height: 20px;
}

/* Eligible Content */
.eligible-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.success-message {
  display: flex;
  gap: 16px;
  align-items: center;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 12px;
  padding: 20px;
}

.success-message .success-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.success-message p {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #FFFFFF;
  margin: 0;
  font-weight: 500;
}

.success-message strong {
  color: #22C55E;
  font-weight: 700;
}

.pitch-now-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: fit-content;
}

.pitch-now-btn:hover {
  background: #A63622;
  transform: translateY(-2px);
}

.pitch-now-btn .btn-icon {
  width: 20px;
  height: 20px;
}

/* Pitch Form Container */
.pitch-form-container {
  background: #1A1A1A;
  border: 1px solid #333333;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
}

.pitch-form-header {
  margin-bottom: 32px;
}

.go-back-btn {
  background: none;
  border: none;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 16px;
  transition: color 0.3s ease;
}

.go-back-btn:hover {
  color: #FFFFFF;
}

.form-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
}

/* Form Styles */
.pitch-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.form-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
}

.form-description {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #888888;
  margin: 0;
  font-weight: 400;
}

.form-input,
.form-textarea,
.form-select {
  padding: 16px 20px;
  background: #262626;
  border: 1px solid #404040;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  outline: none;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  border-color: #BF4129;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-select {
  cursor: pointer;
}

.submit-btn {
  padding: 16px 32px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: fit-content;
  align-self: flex-start;
}

.submit-btn:hover {
  background: #A63622;
  transform: translateY(-2px);
}

/* Success Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.success-modal {
  background: #1A1A1A;
  border: 1px solid #333333;
  border-radius: 20px;
  padding: 40px;
  max-width: 500px;
  width: 90%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.success-modal .success-icon {
  width: 64px;
  height: 64px;
}

.success-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
}

.success-message,
.success-note {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #CCCCCC;
  margin: 0;
  line-height: 1.6;
  font-weight: 400;
}

.success-btn {
  padding: 16px 32px;
  background: #F59E0B;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.success-btn:hover {
  background: #D97706;
  transform: translateY(-2px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-header {
    padding: 80px 20px 20px;
    flex-direction: column;
    gap: 24px;
  }

  .user-header::before {
    height: 480px; /* Extended further to fully contain header controls */
  }

  .user-greeting {
    width: 100%;
  }

  .greeting-text {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .greeting-subtitle {
    margin-bottom: 24px;
  }

  .header-controls {
    position: static;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    padding: 0;
  }

  .balance-toggle {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    min-width: auto;
  }

  .stake-esvc-btn {
    width: auto;
    min-width: 140px;
    justify-content: center;
    padding: 12px 16px;
    font-size: 14px;
    margin: 0;
  }

  .get-funding-container .dashboard-layout {
    flex-direction: column;
    gap: 20px;
    padding: 0 20px;
    margin-top: 60px; /* Consistent spacing between header controls and navbar */
  }

  /* Mobile Sidenav positioning to match other pages */
  .get-funding-container .user-sidenav-container {
    margin-top: 20px; /* Add space above sidenav */
    order: 1;
  }

  .get-funding-container .funding-main-content {
    order: 2;
  }

  .funding-main-content {
    max-width: 100%;
  }

  .eligibility-tabs {
    width: 100%;
    justify-content: center;
  }

  .eligibility-tab {
    flex: 1;
    font-size: 14px;
    padding: 10px 16px;
    min-width: auto;
  }

  .funding-card {
    padding: 24px 20px;
  }

  .funding-title {
    font-size: 20px;
  }

  .warning-message,
  .success-message {
    flex-direction: column;
    text-align: center;
    gap: 12px;
    padding: 16px;
  }

  .warning-icon,
  .success-message .success-icon {
    align-self: center;
  }

  .stake-qualify-btn,
  .pitch-now-btn {
    width: 100%;
    justify-content: center;
    font-size: 14px;
    padding: 14px 20px;
  }

  .pitch-form-container {
    padding: 24px 20px;
  }

  .form-title {
    font-size: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-input,
  .form-textarea,
  .form-select {
    font-size: 14px;
    padding: 14px 16px;
  }

  .submit-btn {
    width: 100%;
    justify-content: center;
    align-self: stretch;
  }

  .success-modal {
    padding: 32px 24px;
    margin: 20px;
  }

  .success-modal .success-icon {
    width: 48px;
    height: 48px;
  }

  .success-title {
    font-size: 20px;
  }

  .success-message,
  .success-note {
    font-size: 14px;
  }

  .success-btn {
    width: 100%;
    justify-content: center;
  }
}
