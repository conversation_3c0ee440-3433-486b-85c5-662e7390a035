/* User Overview Content */
.user-overview-content {
  position: relative;
  z-index: 2;
}

/* Additional gradient blur under body */
.user-overview-content::before {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  left: 0px;
  top: 400px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

.user-overview-content::after {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  right: 0px;
  top: 600px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}

/* User Header */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 60px 40px 40px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Desktop: Position controls outside greeting */
@media (min-width: 769px) {
  .user-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .user-greeting {
    flex: 1;
    position: relative;
  }

  .header-controls {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }


}

/* User Header Background */
.user-header::before {
  content: '';
  position: absolute;
  top: -120px;
  left: -50vw;
  right: -50vw;
  height: 320px;
  background: #260D08;
  z-index: -1;
}



/* Mobile: Adjust red background height */
@media (max-width: 768px) {
  .user-header::before {
    top: -120px;
    bottom: -200px; /* Extend down significantly to cover all content */
    height: auto; /* Let bottom positioning control the height */
  }


}

/* Gradient Blur under greeting */
.user-header::after {
  content: '';
  position: absolute;
  width: 316px;
  height: 316px;
  left: 0px;
  top: 79px;
  background: #CC6754;
  opacity: 0.3;
  filter: blur(132.218px);
  z-index: -1;
}



.greeting-text {
  font-size: 48px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
}

.greeting-subtitle {
  font-size: 16px;
  color: #CCCCCC;
  margin: 0 0 60px 0;
  font-family: 'Montserrat', sans-serif;
}



.balance-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  font-size: 14px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #404040;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #BF4129;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.stake-esvc-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #BF4129;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-esvc-btn:hover {
  background: #A63622;
  transform: translateY(-2px);
}

.btn-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

/* Section Styling */
.summary-section,
.wallet-section,
.roi-section,
.unstake-section {
  margin-bottom: 48px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 24px 0;
  font-family: 'Montserrat', sans-serif;
}

/* Summary Section */
.summary-section {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.summary-section .section-title {
  margin: 0 0 24px 0;
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
}

/* Summary Grid */
.summary-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 0;
  background: rgba(26, 26, 26, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
}

.summary-card {
  background: none;
  border: none;
  padding: 0;
  box-shadow: none;
}

.summary-card:first-child {
  display: flex;
  flex-direction: column;
  gap: 24px;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  padding-right: 24px;
}

.summary-card:nth-child(2) {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  padding-left: 24px;
  padding-right: 24px;
}

.summary-card:nth-child(3) {
  padding-left: 24px;
}

.card-header {
  margin-bottom: 8px;
}

.card-label {
  font-size: 12px;
  color: #999999;
  font-weight: 500;
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 4px;
  font-family: 'Montserrat', sans-serif;
}

.card-unit {
  font-size: 16px;
  color: #CCCCCC;
  font-weight: 500;
  margin-left: 4px;
}

.card-change {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  font-family: 'Montserrat', sans-serif;
}

.card-change.positive {
  color: #4ADE80;
}

.change-icon {
  width: 16px;
  height: 16px;
}

.card-note {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
  font-family: 'Montserrat', sans-serif;
}

/* Individual Wallet Summary */
.wallet-summary {
  margin-top: 40px;
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Wallet Summary */
.wallet-summary .section-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
}

/* Wallet Section */
.wallet-header-row {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 24px;
}

.wallet-selector {
  flex-shrink: 0;
}

.wallet-dropdown {
  width: 280px;
  padding: 12px 16px;
  background: rgba(38, 38, 38, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  cursor: pointer;
}

.wallet-info {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex: 1;
}

.info-icon {
  width: 16px;
  height: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.info-text {
  font-size: 14px;
  color: #CCCCCC;
  line-height: 1.5;
  font-family: 'Montserrat', sans-serif;
}

.wallet-stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  margin: 24px 0;
}

.wallet-stat-card {
  background: rgba(26, 26, 26, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  min-height: auto;
  display: flex;
  flex-direction: column;
}

/* Card Content Layout */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-content-dual {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.card-section {
  flex: 1;
}

/* ROI Section - Inside wallet card */
.roi-section {
  margin-top: 24px;
  padding: 16px;
  background: #1D1104;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
}

.roi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.roi-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
}

.roi-available {
  font-size: 16px;
  color: #886744;
  font-weight: 600;
  font-family: 'Montserrat', sans-serif;
}

.roi-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ADE80 0%, #22C55E 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
}

.progress-earned {
  color: #4ADE80;
}

.progress-expected {
  color: #CCCCCC;
}

.withdraw-roi-btn {
  padding: 12px 24px;
  background: #c6741b;
  color: #FFFFFF;
  border: none;
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.withdraw-roi-btn:hover {
  background: #b5661a;
  transform: translateY(-2px);
}

/* ROI Info - Inside ROI section */
.roi-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #0c1d38;
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  margin-top: 16px;
}

/* Unstake Section - Inside wallet card, no separate card */
.unstake-section-inner {
  margin-top: 40px;
  padding-top: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.unstake-title {
  margin: 0 0 24px 0;
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
}

.unstake-grid {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 40px;
}

.unstake-info {
  flex: 1;
}

.unstake-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.unstake-item:last-child {
  border-bottom: none;
}

.unstake-label {
  font-size: 12px;
  color: #999999;
  font-weight: 500;
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.unstake-value {
  font-size: 16px;
  color: #FFFFFF;
  font-weight: 600;
  font-family: 'Montserrat', sans-serif;
}

.unstake-btn {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.unstake-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-header {
    flex-direction: column;
    gap: 0;
    padding: 80px 20px 80px; /* Increased bottom padding to contain header controls */
    align-items: stretch;
  }

  .user-greeting {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .greeting-text {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .greeting-subtitle {
    margin-bottom: 24px;
  }

  .header-controls {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    padding: 0;
  }

  .balance-toggle {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    min-width: auto;
  }

  .stake-esvc-btn {
    width: auto;
    min-width: 140px;
    justify-content: center;
    padding: 12px 16px;
    font-size: 14px;
    margin: 0;
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px;
    gap: 24px;
    margin-top: 60px; /* Consistent spacing between header controls and navbar */
  }

  /* Mobile Sidenav - Borrowed from Overview page */
  .dashboard-sidebar {
    width: 100%;
    position: static;
    order: 1;
    top: auto;
    padding: 8px 12px;
    margin-bottom: 20px;
    border-radius: 12px;
    height: 60px;
    display: flex;
    align-items: center;
  }

  .section-title {
    font-size: 20px;
  }

  .summary-grid {
    display: flex;
    flex-direction: column;
    gap: 0;
    background: rgba(26, 26, 26, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 0;
  }

  .summary-card {
    background: none;
    border: none;
    padding: 20px 24px;
    box-shadow: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .summary-card:first-child {
    border-right: none;
    padding-right: 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .summary-card:nth-child(2) {
    border-right: none;
    padding-left: 24px;
    padding-right: 24px;
  }

  .summary-card:nth-child(3) {
    padding-left: 24px;
    border-bottom: none;
  }

  .wallet-stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .wallet-stat-card {
    padding: 20px;
  }

  .card-value {
    font-size: 28px;
  }

  .wallet-header-row {
    flex-direction: column;
    gap: 16px;
  }

  .wallet-dropdown {
    width: 100%;
  }

  .unstake-grid {
    flex-direction: column;
    gap: 24px;
    align-items: stretch;
  }

  .unstake-btn {
    width: 100%;
    padding: 16px 24px;
  }

  .roi-info {
    flex-direction: row;
    align-items: center;
    gap: 8px;
    text-align: left;
  }

  .progress-labels {
    flex-direction: column;
    gap: 8px;
  }

  .withdraw-roi-btn {
    width: 100%;
    padding: 16px 24px;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.modal-content {
  background: #262626;
  border-radius: 16px;
  max-width: 560px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
}

.modal-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: #CCCCCC;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
}

.modal-body {
  padding: 24px;
}

.modal-description {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  line-height: 1.5;
  margin: 0 0 24px 0;
}

.withdrawal-stats {
  background: #1A1A1A;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-icon {
  width: 24px;
  height: 24px;
}

.stat-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #FFFFFF;
  font-weight: 500;
}

.stat-row {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
}

.stat-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #CCCCCC;
  font-weight: 500;
  text-transform: uppercase;
}

.stat-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  color: #FFFFFF;
  font-weight: 600;
}

.eligibility-status {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 8px;
  padding: 12px;
}

.status-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon img {
  width: 100%;
  height: 100%;
}

.status-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #22C55E;
  font-weight: 500;
}

.form-section {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #FFFFFF;
  font-weight: 500;
  margin-bottom: 12px;
}

.input-group {
  margin-bottom: 12px;
}

.form-input {
  width: 100%;
  background: #1A1A1A;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 12px 16px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #FFFFFF;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #BF4129;
}

.form-input::placeholder {
  color: #666666;
}

.form-input[type="number"] {
  -moz-appearance: textfield;
}

.form-input[type="number"]::-webkit-outer-spin-button,
.form-input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.form-note {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #CCCCCC;
  margin: 0;
}

.note-icon {
  width: 16px;
  height: 16px;
}

.modal-footer {
  padding: 0 24px 24px;
}

.withdraw-submit-btn {
  width: 100%;
  background: #BF4129;
  border: none;
  border-radius: 8px;
  padding: 16px 24px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.3s ease;
}

.withdraw-submit-btn:hover:not(:disabled) {
  background: #A63622;
}

.withdraw-submit-btn:disabled {
  background: #404040;
  color: #666666;
  cursor: not-allowed;
}

/* Minimum and Success Modal Styles */
.minimum-modal,
.success-modal {
  text-align: center;
  padding: 40px 24px;
  max-width: 400px;
  position: relative;
}

.minimum-modal .modal-close,
.success-modal .modal-close {
  position: absolute;
  top: 16px;
  right: 16px;
}

.modal-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.warning-icon,
.success-icon {
  width: 64px;
  height: 64px;
}

.minimum-modal .modal-title,
.success-modal .modal-title {
  font-size: 20px;
  margin-bottom: 16px;
  text-align: center;
}

.minimum-modal .modal-description,
.success-modal .modal-description {
  text-align: center;
  margin-bottom: 32px;
}

.modal-button {
  background: #BF4129;
  border: none;
  border-radius: 8px;
  padding: 12px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-button:hover {
  background: #A63622;
}

/* Mobile Modal Responsive */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 16px;
  }

  .modal-content {
    max-width: 100%;
    border-radius: 12px;
  }

  .modal-header {
    padding: 20px 20px 0;
  }

  .modal-title {
    font-size: 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .stat-row {
    flex-direction: column;
    gap: 16px;
  }

  .stat-value {
    font-size: 20px;
  }

  .modal-footer {
    padding: 0 20px 20px;
  }

  .minimum-modal,
  .success-modal {
    padding: 40px 24px;
    max-width: 90%;
  }

  .warning-icon,
  .success-icon {
    width: 64px;
    height: 64px;
  }

  /* Mobile: Show icons in front of text */
  .minimum-modal .modal-title,
  .success-modal .modal-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    flex-direction: row;
    margin-bottom: 24px;
  }

  .minimum-modal .modal-icon,
  .success-modal .modal-icon {
    margin-bottom: 0;
    order: -1;
  }

  .minimum-modal .modal-icon img,
  .success-modal .modal-icon img {
    width: 32px;
    height: 32px;
  }

  .modal-description {
    margin-bottom: 32px;
    line-height: 1.6;
    text-align: center;
  }

  .modal-actions {
    margin-top: 32px;
  }
}
