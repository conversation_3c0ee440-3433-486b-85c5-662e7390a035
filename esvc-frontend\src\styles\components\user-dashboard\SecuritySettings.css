/* Security Settings Container */
.security-settings-container {
  min-height: 100vh;
  background: #0A0A0A;
  position: relative;
}

.security-settings-content {
  padding: 40px 60px 40px 320px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* User Header Background - Override general layout */
.security-settings-container .user-header::before {
  content: '';
  position: absolute;
  top: -120px;
  left: -50vw;
  right: -50vw;
  height: 360px !important; /* Calculated to cover greeting + controls */
  background: #260D08;
  z-index: -1;
}

/* Mobile: Adjust red background height */
@media (max-width: 768px) {
  .security-settings-container .user-header::before {
    top: -120px;
    bottom: -300px; /* Extend down much further to cover all mobile content */
    height: 385px !important; /* Let bottom positioning control the height */
  }
}

/* User Greeting Styles */
.greeting-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.greeting-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  color: #CCCCCC;
  margin: 0 0 32px 0;
  font-weight: 400;
}

/* Header Controls */
.stake-esvc-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-esvc-btn:hover {
  background: #A63622;
  transform: translateY(-2px);
}

.stake-esvc-btn .btn-icon {
  width: 20px;
  height: 20px;
}

/* Balance Toggle */
.balance-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #404040;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: #FFFFFF;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #BF4129;
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

/* User Header Layout */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 48px;
  position: relative;
  z-index: 1;
}

.user-greeting {
  flex: 1;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 24px;
  position: absolute;
  right: 0;
  top: 0;
}

/* Security Settings Main */
.security-settings-main {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid #333333;
  border-radius: 20px;
  padding: 32px;
  margin-top: 24px;
}

.settings-header {
  margin-bottom: 32px;
}

.settings-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 28px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
}

/* Security Options */
.security-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.security-option {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  background: #1A1A1A;
  border: 1px solid #333333;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.security-option:hover {
  background: #222222;
  border-color: #BF4129;
  transform: translateY(-2px);
}

.option-icon {
  width: 48px;
  height: 48px;
  background: #333333;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.security-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}

.option-icon svg {
  width: 24px;
  height: 24px;
  color: #F59E0B;
}

.option-content {
  flex: 1;
}

.option-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.option-description {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
}

.option-arrow {
  font-size: 20px;
  color: #BF4129;
  font-weight: 600;
}

/* Dashboard Layout */
.security-settings-container .dashboard-layout {
  display: flex;
  gap: 32px;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .security-settings-content {
    padding: 20px;
    margin-top: 0;
  }

  .security-settings-container .dashboard-layout {
    flex-direction: column;
    gap: 20px;
    padding: 0 20px;
    margin-top: 40px; /* Reduced spacing to shift navbar up */
  }

  /* Mobile Sidenav positioning to match other pages */
  .security-settings-container .user-sidenav-container {
    margin-top: 20px; /* Add space above sidenav */
    order: 1;
  }

  .security-settings-container .security-settings-main {
    order: 2;
  }

  .user-header {
    padding: 80px 20px 20px;
    flex-direction: column;
    gap: 24px;
    margin-bottom: 32px;
  }

  .user-greeting {
    width: 100%;
  }

  .greeting-text {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .greeting-subtitle {
    margin-bottom: 24px;
  }

  .header-controls {
    position: static;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    padding: 0;
  }

  .security-settings-main {
    padding: 24px;
    margin-top: 16px;
  }

  .settings-title {
    font-size: 24px;
  }

  .security-option {
    padding: 20px;
    gap: 16px;
  }

  .option-icon {
    width: 40px;
    height: 40px;
  }

  .security-icon {
    width: 20px;
    height: 20px;
  }

  .option-title {
    font-size: 18px;
  }

  .option-description {
    font-size: 13px;
  }

  .option-arrow {
    font-size: 18px;
  }
}
